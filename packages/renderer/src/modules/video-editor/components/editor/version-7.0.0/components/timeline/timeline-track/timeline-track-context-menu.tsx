import React, { FC, useMemo } from 'react'
import { IndexableTrack, OverlayType, TrackType } from '@app/rve-shared/types'
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from '@/components/ui/context-menu.tsx'
import { ClipboardPaste, Plus } from 'lucide-react'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'
import { isOverlayAcceptableByTrack } from '@rve/editor/utils/track-helper.ts'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'
import { byStartFrame } from '@rve/editor/utils/overlay-helper.ts'

type TimelineTrackContextMenuProps = React.PropsWithChildren<{
  track: IndexableTrack
}>

const AddCaptionMenuItem: FC<{ track: IndexableTrack }> = ({ track }) => {
  const { videoPlayer: { currentFrame } } = useEditorContext()

  const { addOverlayToTrack, generateDefaultCaptionOverlay } = useOverlayHelper()

  const enableToAddTextOverlay = useMemo(() => {
    return !track.overlays
      .filter(o => o.type === OverlayType.TEXT)
      .some(o => o.from <= currentFrame && o.from + o.durationInFrames >= currentFrame)
  }, [track, currentFrame])

  if (track.type !== TrackType.NARRATION) return null

  return (
    <ContextMenuItem
      className="dark:hover:bg-slate-800 dark:focus:bg-slate-800 dark:text-slate-200"
      disabled={!enableToAddTextOverlay}
      onClick={e => {
        e.stopPropagation()
        const newOverlay = generateDefaultCaptionOverlay({ from: currentFrame ?? undefined })
        const nextOverlay = track.overlays
          .filter(o => o.type === OverlayType.TEXT)
          .sort(byStartFrame()).find(o => o.from > newOverlay.from)

        if (nextOverlay) {
          newOverlay.durationInFrames = Math.min(newOverlay.durationInFrames, nextOverlay.from - newOverlay.from)
        }

        addOverlayToTrack(track.index, newOverlay)
      }}
    >
      <Plus className="mr-4 h-4 w-4" />
      添加字幕
    </ContextMenuItem>
  )
}

const PasteMenuItem: FC<{ track: IndexableTrack }> = ({ track }) => {
  const {
    mouseOnCurrentFrame,
    clipboard: { clipped, pasteOverlay },
  } = useTimeline()

  return (
    <ContextMenuItem
      disabled={!clipped || !isOverlayAcceptableByTrack(clipped, track)}
      className="dark:hover:bg-slate-800 dark:focus:bg-slate-800 dark:text-slate-200"
      onClick={e => {
        e.stopPropagation()
        if (mouseOnCurrentFrame !== null) {
          pasteOverlay(track.index, mouseOnCurrentFrame)
        }
      }}
    >
      <ClipboardPaste className="mr-4 h-4 w-4" />
      粘贴
    </ContextMenuItem>
  )
}

export const TimelineTrackContextMenu: React.FC<TimelineTrackContextMenuProps> = ({ children, track }) => {
  const { mouseOnCurrentFrame, setIsContextMenuOpen, } = useTimeline()

  const { videoPlayer: { seekTo } } = useEditorContext()

  return (
    <ContextMenu
      onOpenChange={open => {
        if (open && mouseOnCurrentFrame !== null) {
          seekTo(mouseOnCurrentFrame)
        }
        setIsContextMenuOpen(open)
      }}
    >
      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
      <ContextMenuContent className="dark:bg-slate-900 dark:border-slate-800">
        <AddCaptionMenuItem track={track} />
        <PasteMenuItem track={track} />
      </ContextMenuContent>
    </ContextMenu>
  )
}
