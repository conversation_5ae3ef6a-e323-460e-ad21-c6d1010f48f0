import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ScriptSceneData, useQueryScript } from '@/hooks/queries/useQueryScript.ts'
import { useEditorContext } from '@rve/editor/contexts'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Download, Edit, Mic2 } from 'lucide-react'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore.ts'
import { LoadingIndicator } from '@/components/LoadingIndicator.tsx'
import { estimateSentencesDuration, extractSentencesFromScriptScene } from '@/libs/tools/script.ts'
import { OverlayType, SoundOverlay, StoryboardOverlay, TextOverlay, Track, TrackType } from '@app/rve-shared/types'
import { generateNewOverlayId } from '@rve/editor/utils/track-helper.ts'
import {
  DEFAULT_OVERLAY,
  DEFAULT_TEXT_FONT_SRC,
  DEFAULT_TEXT_OVERLAY_STYLES,
  FPS,
  TEXT_TO_SPEECH_CONFIG
} from '@rve/editor/constants'
import { EVENT_NAMES, useEventListener } from '@rve/editor/hooks/useEventBus'
import { cn } from '@/components/lib/utils'
import { AiModule } from '@/libs/request/api/ai'
import {
  TextToSpeechProgressOverlay,
  TTSProgressState,
  TTSTaskStatus
} from '@rve/editor/components/TextToSpeechProgressOverlay.tsx'
import { TTSErrorReportDialog } from '@rve/editor/components/TTSErrorReportDialog.tsx'
import { toast } from 'react-toastify'
import { cacheManager } from '@/libs/cache/cache-manager.ts'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'

type AudioInfo = {
  durationInFrames: number;
  localUrl?: string;
  originalUrl?: string;
  error?: string
}

type AudioInfoMap = Map<string, AudioInfo>

type IndexedSentence = {
  text: string;
  sceneIndex: number;
  sentenceIndex: number
}

// 脚本段落组件
export const ScriptScene: React.FC<{
  scene: ScriptSceneData
  index: number
}> = ({ scene, index }) => {
  // 将脚本内容按行分割成句子
  const sentences = useMemo(() => extractSentencesFromScriptScene(scene), [scene])

  const [minDuration, maxDuration] = estimateSentencesDuration(sentences)

  return (
    <div className="mb-6">
      {/* 段落标题 */}
      <div className="flex items-center gap-2 mb-3">
        <h3 className="text-md font-medium">
          #{index + 1} {scene.title || '未命名分镜'}
        </h3>
        {minDuration && (
          <div className="text-xs text-gray-500">
            预计时长 {minDuration}s {maxDuration && `~ ${maxDuration}s`}
          </div>
        )}
      </div>

      {/* 句子列表 */}
      <SentenceList sentences={sentences} />

      {/* 画面说明 */}
      {scene.notes && (
        <div className="mt-3">
          <div className="text-xs font-medium text-gray-700 mb-1">画面说明</div>
          <div className="text-xs text-gray-600  p-2 rounded">
            {scene.notes}
          </div>
        </div>
      )}

      {/* 拍摄示例 */}
      {/*<div className="mt-3">*/}
      {/*  <div className="text-xs font-medium text-gray-700 mb-1">拍摄示例  </div>*/}
      {/*  <div className="text-xs text-gray-600" />*/}
      {/*</div>*/}
    </div>
  )
}

// 句子列表组件
export const SentenceList: React.FC<{
  sentences: string[]
}> = ({ sentences }) => {
  return (
    <div className="space-y-1">
      {sentences.map((sentence, index) => (
        <div key={index} className="flex items-start gap-2 text-xs">
          <span className="text-gray-400 text-xs mt-0.5 min-w-[20px]">
            {index + 1}.
          </span>
          <span className="text-gray-400 leading-relaxed">
            {sentence}
          </span>
        </div>
      ))}
    </div>
  )
}

/**
 * 计算句子的时长（帧数）
 */
const calculateSentenceDurationInFrames = (sentence: string): number => {
  // 简单预估10个字符为 1 秒，转换为帧数
  const durationInSeconds = sentence.length / 10
  return Math.max(FPS * durationInSeconds, FPS) // 最少1秒
}

const useGenerateTextOnly = (scenes: ScriptSceneData[]) => {
  const { tracks, updateTracks } = useEditorContext()
  const { generateDefaultCaptionOverlay } = useOverlayHelper()

  /**
   * 生成分镜轨道和口播轨道
   */
  const generateTracksFromScript = useCallback(
    (scenes: ScriptSceneData[], existingTracks: Track[]): Track[] => {
      const MINIMUM_DURATION = FPS

      if (!scenes.length) return []

      // 获取下一个可用的 overlay ID
      let nextOverlayId = generateNewOverlayId(existingTracks)

      // 1. 生成分镜轨道
      const storyboardTrack: Track = {
        type: TrackType.STORYBOARD,
        overlays: []
      }

      let currentFrame = 0

      // 为每个 scene 创建分镜 overlay
      scenes.forEach(scene => {
        const sentences = extractSentencesFromScriptScene(scene)

        // 计算该分镜的时长：取所有句子预估时长的最大值
        const [minDuration, maxDuration = minDuration] = estimateSentencesDuration(sentences)
        const sceneDurationInFrames = maxDuration
          ? Math.max(maxDuration * FPS, MINIMUM_DURATION)
          : MINIMUM_DURATION

        const storyboardOverlay: StoryboardOverlay = {
          ...DEFAULT_OVERLAY,
          id: nextOverlayId++,
          type: OverlayType.STORYBOARD,
          from: currentFrame,
          durationInFrames: sceneDurationInFrames,
        }

        storyboardTrack.overlays.push(storyboardOverlay)
        currentFrame += sceneDurationInFrames
      })

      // 2. 计算需要的口播轨道数量（所有 scenes 中的最大句子数量）
      const sentenceCounts = scenes.map(scene =>
        extractSentencesFromScriptScene(scene).length
      )
      const maxSentenceCount = sentenceCounts.length > 0 ? Math.max(...sentenceCounts) : 0

      // 3. 生成口播轨道
      const narrationTracks: Track[] = []

      for (let trackIndex = 0; trackIndex < maxSentenceCount; trackIndex++) {
        const narrationTrack: Track = {
          type: TrackType.NARRATION,
          overlays: []
        }

        let sceneStartFrame = 0

        // 为每个 scene 在当前口播轨道中添加对应的句子
        scenes.forEach((scene, sceneIndex) => {
          const sentences = extractSentencesFromScriptScene(scene)
          const sentence = sentences[trackIndex] // 第 trackIndex 个句子

          if (sentence && sentence.trim()) {
            // 计算句子时长
            const sentenceDurationInFrames = calculateSentenceDurationInFrames(sentence)

            narrationTrack.overlays.push(
              generateDefaultCaptionOverlay({
                id: nextOverlayId++,
                content: sentence.trim(),
                storyboardIndex: sceneIndex, // 关联到对应的分镜
                from: sceneStartFrame,
                durationInFrames: sentenceDurationInFrames,
              })
            )
          }

          // 更新下一个分镜的起始帧位置
          const storyboardOverlay = storyboardTrack.overlays[sceneIndex]
          if (storyboardOverlay) {
            sceneStartFrame += storyboardOverlay.durationInFrames
          }
        })

        narrationTracks.push(narrationTrack)
      }

      return [storyboardTrack, ...narrationTracks]
    },
    [generateDefaultCaptionOverlay]
  )

  return useCallback(() => {
    if (!scenes.length) {
      console.warn('没有可导入的脚本内容')
      return
    }

    // 生成新的分镜轨道和口播轨道
    const newTracks = generateTracksFromScript(scenes, tracks)

    if (!newTracks.length) {
      console.warn('生成的轨道为空')
      return
    }

    // 替换现有的分镜轨道和口播轨道，保留其他类型轨道
    updateTracks(prevTracks => {
      const filteredTracks = prevTracks.filter(track =>
        track.type !== TrackType.STORYBOARD && track.type !== TrackType.NARRATION
      )

      return [...newTracks, ...filteredTracks]
    })
  }, [scenes, tracks, updateTracks, generateTracksFromScript])
}

const useGenerateTextAndAudio = (scenes: ScriptSceneData[]) => {
  const { tracks, updateTracks, getAspectRatioDimensions } = useEditorContext()

  // 文本转语音状态
  const [ttsProgressState, setTtsProgressState] = useState<TTSProgressState>({
    visible: false,
    completed: 0,
    total: 0,
    tasks: []
  })

  // 错误报告弹窗状态
  const [errorReportOpen, setErrorReportOpen] = useState(false)
  const [failedTasks, _setFailedTasks] = useState<TTSTaskStatus[]>([])

  /**
   * 基于带音频信息的句子列表生成轨道
   */
  const generateTracksFromAudioInfo = useCallback((
    sentences: Array<IndexedSentence & { audioInfo?: AudioInfo }>,
    width: number,
    height: number
  ): Track[] => {
    const MINIMUM_DURATION = FPS
    let nextOverlayId = generateNewOverlayId(tracks)

    // 按场景组织句子和音频信息
    const sceneData = new Map<number, {
      sentences: Array<{ text: string, sentenceIndex: number, audioInfo?: AudioInfo }>
      totalDuration: number
    }>(
      scenes.map((_, index) => {
        return [index, { sentences: [], totalDuration: MINIMUM_DURATION }]
      })
    )

    // 填充句子和音频信息
    sentences.forEach(sentence => {
      const sceneInfo = sceneData.get(sentence.sceneIndex)
      if (!sceneInfo) return
      const { audioInfo } = sentence

      const sentenceData = {
        text: sentence.text,
        sentenceIndex: sentence.sentenceIndex,
        audioInfo
      }

      sceneInfo.sentences.push(sentenceData)

      // 计算场景总时长（取最长的句子时长）
      if (audioInfo) {
        sceneInfo.totalDuration = Math.max(sceneInfo.totalDuration, audioInfo.durationInFrames)
      }
    })

    // 生成分镜轨道
    const storyboardTrack: Track = {
      type: TrackType.STORYBOARD,
      overlays: []
    }

    let currentFrame = 0
    scenes.forEach((_, sceneIndex) => {
      const sceneInfo = sceneData.get(sceneIndex)
      if (!sceneInfo) return

      const storyboardOverlay: StoryboardOverlay = {
        ...DEFAULT_OVERLAY,
        id: nextOverlayId++,
        type: OverlayType.STORYBOARD,
        from: currentFrame,
        durationInFrames: sceneInfo.totalDuration,
      }

      storyboardTrack.overlays.push(storyboardOverlay)
      currentFrame += sceneInfo.totalDuration
    })

    // 计算需要的口播轨道数量
    const maxSentenceCount = Math.max(
      ...Array
        .from(sceneData.values())
        .map(info => info.sentences.length)
    )

    // 生成口播轨道
    const narrationTracks: Track[] = []

    for (let trackIndex = 0; trackIndex < maxSentenceCount; trackIndex++) {
      const narrationTrack: Track = {
        type: TrackType.NARRATION,
        overlays: []
      }

      let sceneStartFrame = 0

      scenes.forEach((_, sceneIndex) => {
        const sceneInfo = sceneData.get(sceneIndex)
        if (!sceneInfo) return

        const sentence = sceneInfo.sentences[trackIndex]
        if (!sentence || !sentence.text.trim()) {
          // 更新下一个分镜的起始帧位置
          sceneStartFrame += sceneInfo.totalDuration
          return
        }

        // 创建TextOverlay
        const textDuration = sentence.audioInfo?.durationInFrames || Math.max(FPS, sentence.text.length / 10 * FPS)

        const textOverlay: TextOverlay = {
          ...DEFAULT_OVERLAY,
          id: nextOverlayId++,
          type: OverlayType.TEXT,
          content: sentence.text.trim(),
          src: DEFAULT_TEXT_FONT_SRC,
          from: sceneStartFrame,
          durationInFrames: textDuration,
          width: width * 0.8,
          height: 100,
          left: width * 0.1,
          top: height * 0.8,
          storyboardIndex: sceneIndex,
          styles: {
            ...DEFAULT_TEXT_OVERLAY_STYLES,
            textAlign: 'center',
          }
        }

        narrationTrack.overlays.push(textOverlay)

        // 如果有音频且TTS成功（有原始URL），创建对应的SoundOverlay
        if (sentence.audioInfo && sentence.audioInfo.originalUrl) {
          const soundOverlay: SoundOverlay = {
            id: nextOverlayId++,
            type: OverlayType.SOUND,
            content: sentence.audioInfo.originalUrl, // 使用原始远程URL
            src: sentence.audioInfo.originalUrl,     // 使用原始远程URL
            localSrc: sentence.audioInfo.localUrl,   // 使用本地缓存URL
            durationInFrames: sentence.audioInfo.durationInFrames,
            from: sceneStartFrame,
            height: 100,
            width: 200,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            storyboardIndex: sceneIndex,
            styles: {
              volume: 1,
            },
          }

          narrationTrack.overlays.push(soundOverlay)
        }

        // 更新下一个分镜的起始帧位置
        sceneStartFrame += sceneInfo.totalDuration
      })

      narrationTracks.push(narrationTrack)
    }

    return [storyboardTrack, ...narrationTracks]
  }, [scenes, tracks])

  /**
   * 直接使用已处理的音频信息生成轨道
   */
  const generateTracksWithAudioInfo = useCallback(async (
    allSentences: IndexedSentence[],
    audioInfoMap: AudioInfoMap
  ) => {
    try {
      const { width, height } = getAspectRatioDimensions()

      const sentences: Array<IndexedSentence & { audioInfo?: AudioInfo }> = allSentences
        .map(sentence => ({
          ...sentence,
          audioInfo: audioInfoMap.get(sentence.text)
        }))

      const newTracks = generateTracksFromAudioInfo(sentences, width, height)

      // 更新轨道
      updateTracks(prevTracks => {
        const filteredTracks = prevTracks.filter(track =>
          track.type !== TrackType.STORYBOARD &&
          track.type !== TrackType.NARRATION
        )

        return [...newTracks, ...filteredTracks]
      })

      // 隐藏进度遮罩
      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })

      // 显示结果
      const audioErrorCount = Array.from(audioInfoMap.values()).filter(info => info.error).length
      const successCount = allSentences.length - audioErrorCount

      if (audioErrorCount > 0) {
        toast.warning(`成功生成 ${successCount} 个语音轨道，其中 ${audioErrorCount} 个使用了估算时长`)
      } else {
        toast.success(`成功生成 ${successCount} 个语音轨道`)
      }
    } catch (error) {
      console.error('生成音频轨道失败:', error)
      toast.error('生成音频轨道失败，请重试')

      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })
    }
  }, [updateTracks, getAspectRatioDimensions, generateTracksFromAudioInfo])

  const generateTextAndAudio = useCallback(
    async () => {
      if (!scenes.length) {
        toast.warning('没有可朗读的脚本内容')
        return
      }

      // 提取所有台词
      const allSentences: IndexedSentence[] = []
      scenes.forEach((scene, sceneIndex) => {
        const sentences = extractSentencesFromScriptScene(scene)
        sentences.forEach((sentence, sentenceIndex) => {
          if (sentence.trim()) {
            allSentences.push({ text: sentence.trim(), sceneIndex, sentenceIndex })
          }
        })
      })

      if (!allSentences.length) {
        toast.warning('没有找到有效的台词内容')
        return
      }

      // 初始化进度状态 - 简化为基于句子数量的进度计算
      setTtsProgressState({
        visible: true,
        completed: 0,
        total: allSentences.length,
        tasks: []
      })

      const tasks: TTSTaskStatus[] = []
      const completedTasks: TTSTaskStatus[] = []

      try {
        // 第一步：为所有台词创建TTS任务
        for (let i = 0; i < allSentences.length; i++) {
          const sentence = allSentences[i]

          try {
            const response = await AiModule.endpoints.textToSpeech({
              text: sentence.text,
              ...TEXT_TO_SPEECH_CONFIG.defaultParams
            })

            const task: TTSTaskStatus = {
              taskId: response.task_id,
              text: sentence.text,
              isPending: true
            }

            tasks.push(task)
          } catch (error) {
            console.error('创建TTS任务失败:', error)
            const failedTask: TTSTaskStatus = {
              taskId: '',
              text: sentence.text,
              isPending: false,
              error: error instanceof Error ? error.message : '创建任务失败'
            }
            tasks.push(failedTask)
          }
        }

        setTtsProgressState(prev => ({
          ...prev,
          tasks: [...tasks]
        }))

        // 用于跟踪已完成句子处理的数量
        let completedSentencesCount = 0
        const audioInfoMap: AudioInfoMap = new Map()

        // 处理单个句子的完整流程（音频缓存 + 时长提取）
        const processAudio = async (task: TTSTaskStatus) => {
          const audioUrl = task.audioUrl!
          try {
            const localUrl = await cacheManager.resource.cacheResource(ResourceType.SOUND, audioUrl)

            const duration = await window.editor.getAudioDuration(localUrl)
            const audioDurationInFrames = Math.round(duration * FPS)

            audioInfoMap.set(task.text, {
              originalUrl: audioUrl,
              localUrl,
              durationInFrames: audioDurationInFrames
            })
          } catch (error) {
            console.error(`获取音频时长失败: ${task.text}`, error)

            // 使用估算时长作为备选
            const estimatedDurationSeconds = Math.max(1, task.text.length / 10)
            const estimatedDurationInFrames = Math.round(estimatedDurationSeconds * FPS)

            audioInfoMap.set(task.text, {
              originalUrl: audioUrl,
              durationInFrames: estimatedDurationInFrames,
              error: error instanceof Error ? error.message : '获取时长失败'
            })
          } finally {
            // 无论成功失败，都更新进度
            completedSentencesCount++
            setTtsProgressState(prev => ({
              ...prev,
              completed: completedSentencesCount
            }))
          }
        }

        // 第二步：轮询任务状态并并行处理完成的任务
        const pollTasks = async () => {
          const pendingTasks = tasks.filter(task => task.isPending)

          if (pendingTasks.length === 0) {
            return true // 所有任务完成
          }

          for (const task of pendingTasks) {
            try {
              const statusResponse = await AiModule.endpoints.queryTaskStatus(task.taskId)

              if (statusResponse.status === 'SUCCESS') {
                task.isPending = false
                task.audioUrl = statusResponse.result?.data?.video_name
                completedTasks.push(task)

                // 立即开始处理这个句子（并行执行，不等待）
                processAudio(task).catch(error => {
                  console.error('处理句子失败:', error)
                })
              } else if (statusResponse.status === 'FAILURE' || statusResponse.status === 'REVOKED') {
                task.isPending = false
                task.error = '任务执行失败'
                completedTasks.push(task)

                // 失败的任务也需要处理（使用默认时长）
                const estimatedDurationSeconds = Math.max(1, task.text.length / 10)
                const estimatedDurationInFrames = Math.round(estimatedDurationSeconds * FPS)

                audioInfoMap.set(task.text, {
                  durationInFrames: estimatedDurationInFrames,
                  error: task.error || 'TTS任务失败'
                })

                completedSentencesCount++
                setTtsProgressState(prev => ({
                  ...prev,
                  completed: completedSentencesCount
                }))
              }
            } catch (error) {
              console.error('查询任务状态失败:', error)
              // 继续轮询，不标记为失败
            }
          }

          return completedTasks.length >= allSentences.length
        }

        // 等待所有句子处理完成的函数
        const waitForAllSentencesProcessed = async () => {
          return new Promise<void>(resolve => {
            const checkInterval = setInterval(() => {
              if (completedSentencesCount >= allSentences.length) {
                clearInterval(checkInterval)
                resolve()
              }
            }, 500) // 每100ms检查一次
          })
        }

        const handleTimeoutTask = (task: TTSTaskStatus) => {
          task.isPending = false
          task.error = '任务超时'

          // 超时任务也需要处理
          const estimatedDurationSeconds = Math.max(1, task.text.length / 10)
          const estimatedDurationInFrames = Math.round(estimatedDurationSeconds * FPS)

          audioInfoMap.set(task.text, {
            durationInFrames: estimatedDurationInFrames,
            localUrl: '',
            error: '任务超时'
          })

          completedSentencesCount++
          setTtsProgressState(prev => ({
            ...prev,
            completed: completedSentencesCount
          }))
        }

        // 开始轮询
        const startTime = Date.now()
        const pollInterval = setInterval(async () => {
          const allTasksCompleted = await pollTasks()

          if (allTasksCompleted) {
            clearInterval(pollInterval)

            // 处理超时任务
            const timeoutTasks = tasks.filter(task =>
              task.isPending && Date.now() - startTime > TEXT_TO_SPEECH_CONFIG.taskTimeout
            )

            timeoutTasks.forEach(handleTimeoutTask)

            // 等待所有句子处理完成后生成轨道
            await waitForAllSentencesProcessed()
            await generateTracksWithAudioInfo(allSentences, audioInfoMap)
          } else if (Date.now() - startTime > TEXT_TO_SPEECH_CONFIG.taskTimeout) {
            clearInterval(pollInterval)

            // 标记剩余任务为超时
            tasks
              .filter(o => o.isPending)
              .forEach(handleTimeoutTask)

            // 等待所有句子处理完成后生成轨道
            await waitForAllSentencesProcessed()
            await generateTracksWithAudioInfo(allSentences, audioInfoMap)
          }
        }, TEXT_TO_SPEECH_CONFIG.pollInterval)
      } catch (error) {
        console.error('朗读全部台词失败:', error)
        toast.error('朗读功能执行失败，请重试')

        setTtsProgressState({
          visible: false,
          completed: 0,
          total: 0,
          tasks: []
        })
      }
    },
    [scenes]
  )

  const ReportDialog = useCallback(() => (
    <TTSErrorReportDialog
      open={errorReportOpen}
      onOpenChange={setErrorReportOpen}
      failedTasks={failedTasks}
      successCount={ttsProgressState.total - failedTasks.length}
      totalCount={ttsProgressState.total}
    />
  ), [errorReportOpen, failedTasks, ttsProgressState])

  const ProgressOverlay = useCallback(() => (
    <TextToSpeechProgressOverlay progressState={ttsProgressState} />
  ), [ttsProgressState])

  return {
    generateTextAndAudio,
    ReportDialog,
    ProgressOverlay
  }
}

const ScriptPanel = () => {
  const { scriptId } = useEditorContext()
  const { data: script } = useQueryScript(scriptId)
  const { pushNamedTab } = useVirtualTabsStore()

  // 高光特效状态
  const [isHighlighted, setIsHighlighted] = useState(false)

  // 过滤出有内容的场景
  const scenes = useMemo(() => {
    return script?.content || []
  }, [script])

  // 监听高光事件
  useEventListener(EVENT_NAMES.HIGHLIGHT_SCRIPT_BUTTONS, () => {
    setIsHighlighted(true)

    const fadeOutTimer = setTimeout(() => {
      setIsHighlighted(false)
    }, 2000)

    return () => clearTimeout(fadeOutTimer)
  })

  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      setIsHighlighted(false)
    }
  }, [])

  // 高光特效样式类
  const highlightClasses = cn(
    'text-xs h-7 px-3 relative z-10 transition-all duration-600',
    isHighlighted && `
      bg-gradient-brand text-white border-transparent shadow-xl hover:opacity-90
      opacity-100 scale-100 animate-pulse ring-4 ring-[var(--color-primary-highlight)]/30
    `
  )

  // 高光容器样式（用于外层包装）
  const highlightContainerClasses = isHighlighted
    ? cn(
      'relative transition-all duration-600',
      'before:absolute before:-inset-1 before:bg-gradient-brand before:rounded-md before:opacity-20 before:animate-ping before:pointer-events-none'
    )
    : ''

  // 处理按钮点击时取消高光
  const handleButtonClick = (originalHandler?: () => void) => {
    if (isHighlighted) {
      setTimeout(() => {
        setIsHighlighted(false)
        originalHandler?.()
      }, 300) // 快速淡出
    } else {
      // 如果没有高光或已经在淡出，直接执行
      originalHandler?.()
    }
  }

  const generateTextOnly = useGenerateTextOnly(scenes)

  const { generateTextAndAudio, ReportDialog, ProgressOverlay } = useGenerateTextAndAudio(scenes)

  if (!script) return <LoadingIndicator />

  return (
    <div className="h-full flex flex-col w-full">
      {/* 顶部工具栏 */}
      <div className="flex items-center p-4 border-b border-gray-200">
        <div className="flex-grow">
          {script.title}
        </div>

        <Button
          variant="ghost"
          size="sm"
          className="text-xs h-7 px-3 text-gray-400"
          onClick={() => pushNamedTab('Script', { id: script.id.toString() })}
        >
          <Edit className="w-3 h-3 mr-1" />
          编辑脚本
        </Button>

        <div className={cn('flex gap-1', highlightContainerClasses)}>
          <div className={cn('relative', )}>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                highlightClasses
              )}
              onClick={() => handleButtonClick(generateTextOnly)}
            >
              <Download className="w-3 h-3 mr-1" />
              仅导入台词
            </Button>
          </div>

          <div className={cn('relative')}>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                highlightClasses
              )}
              onClick={() => handleButtonClick(generateTextAndAudio)}
            >
              <Mic2 className="w-3 h-3 mr-1" />
              朗读全部台词
            </Button>
          </div>
        </div>

      </div>

      {/* 脚本内容区域 */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {scenes.length > 0 ? (
            scenes.map((scene, index) => (
              <ScriptScene
                key={scene.id}
                scene={scene}
                index={index}
              />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center h-40 text-gray-500">
              <div className="text-sm">暂无脚本内容</div>
              <div className="text-xs mt-1">请先添加脚本内容</div>
            </div>
          )}
        </div>
      </ScrollArea>

      <ReportDialog />
      <ProgressOverlay />
    </div>
  )
}

export default React.memo(ScriptPanel)
